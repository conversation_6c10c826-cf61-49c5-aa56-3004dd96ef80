# MTProxy with Cloudflare WARP

A Docker setup that combines Telegram's MTProxy with Cloudflare WARP for enhanced privacy and bypassing restrictions.

## Features

- 🚀 **MTProxy**: Official Telegram proxy server
- 🔒 **Cloudflare WARP**: Routes traffic through Cloudflare's network for additional privacy
- 🐳 **Docker**: Easy deployment and management
- 🔧 **Configurable**: Environment variables for easy customization
- 📊 **Health Checks**: Built-in monitoring for both services
- 🔄 **Auto-restart**: Automatic service recovery
- 📝 **Logging**: Comprehensive logging for debugging

## Quick Start

### Prerequisites

- Docker and Docker Compose installed
- VPS with public IP address
- Basic knowledge of Docker

### Default Setup

1. Clone or download this repository
2. Run with default settings:

```bash
docker-compose up -d
```

This will:
- Use default secret: `ec4dd80983dbf12d6b354cf7bcfe9a48`
- Expose MTProxy on port 443
- Auto-detect your external IP
- Start with 1 worker process

### Custom Configuration

1. Copy the environment template:

```bash
cp .env.example .env
```

2. Edit `.env` file with your preferred settings:

```bash
# Generate a random secret
SECRET=$(head -c 16 /dev/urandom | xxd -ps)

# Set number of workers (increase for high traffic)
WORKERS=2

# Set custom port
PORT=8443

# Set external IP if auto-detection fails
IP=your.server.ip
```

3. Start the services:

```bash
docker-compose up -d
```

## Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `SECRET` | `ec4dd80983dbf12d6b354cf7bcfe9a48` | 32-character hex secret for MTProxy |
| `WORKERS` | `1` | Number of worker processes |
| `PORT` | `443` | External port to expose |
| `IP` | auto-detect | External IP address |
| `TZ` | `UTC` | Timezone for logs |
| `MTPROTO_REPO_URL` | Official repo | MTProxy source repository |

## Usage

### Connection Links

After starting, check the logs to get your connection links:

```bash
docker-compose logs mtproxy-warp
```

Look for lines like:
```
MTProxy connection link:
  tg://proxy?server=YOUR_IP&port=443&secret=YOUR_SECRET
  With random padding: tg://proxy?server=YOUR_IP&port=443&secret=ddYOUR_SECRET
```

### Random Padding

For better resistance against DPI detection, use the link with `dd` prefix in the secret. This enables random padding on the client side.

### Monitoring

Check service status:
```bash
# View logs
docker-compose logs -f mtproxy-warp

# Check health
docker-compose ps

# View WARP status
docker-compose exec mtproxy-warp warp-cli status
```

## Troubleshooting

### Common Issues

1. **"Connecting" state in Telegram**
   - Check firewall settings
   - Verify port forwarding
   - Ensure external IP is correct

2. **"Updating" state in Telegram**
   - Check system time synchronization
   - Verify WARP connection
   - Check logs for errors

3. **WARP registration fails**
   - Ensure internet connectivity
   - Check DNS settings
   - Try restarting the container

### Debug Commands

```bash
# Check WARP status
docker-compose exec mtproxy-warp warp-cli status

# View detailed logs
docker-compose logs --tail=100 mtproxy-warp

# Restart services
docker-compose restart

# Rebuild container
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Firewall Configuration

Ensure the following ports are open:
- Your chosen MTProxy port (default: 443)
- Outbound HTTPS (443) for WARP registration
- Outbound connections to Telegram servers

## Security Considerations

- Change the default secret before production use
- Use a non-standard port if possible
- Keep the container updated
- Monitor logs for suspicious activity
- Consider using fail2ban for additional protection

## Advanced Usage

### Using Alternative MTProxy Fork

Set in your `.env` file:
```bash
MTPROTO_REPO_URL=https://github.com/GetPageSpeed/MTProxy
```

### Custom Build

Build with specific arguments:
```bash
docker build --build-arg WORKERS=4 --build-arg SECRET=your_secret .
```

### Production Deployment

For production use:
1. Use a strong, unique secret
2. Set up proper monitoring
3. Configure log rotation
4. Use a reverse proxy if needed
5. Set up automated backups

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Disclaimer

This software is provided for educational and legitimate use only. Users are responsible for complying with all applicable laws and regulations in their jurisdiction.
