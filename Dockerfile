# MTProxy with Cloudflare WARP integration
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    libssl-dev \
    zlib1g-dev \
    xxd \
    supervisor \
    ca-certificates \
    gnupg \
    lsb-release \
    net-tools \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Add Cloudflare GPG key and repository
RUN mkdir -p /etc/apt/keyrings && \
    curl -fsSL https://pkg.cloudflare.com/cloudflare-main.gpg | tee /etc/apt/keyrings/cloudflare-main.gpg >/dev/null && \
    echo "deb [signed-by=/etc/apt/keyrings/cloudflare-main.gpg] https://pkg.cloudflare.com/cloudflare-warp $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/cloudflare-client.list

# Install Cloudflare WARP
RUN apt-get update && apt-get install -y cloudflare-warp && rm -rf /var/lib/apt/lists/*

# Build arguments
ARG WORKERS=1
ARG SECRET=ec4dd80983dbf12d6b354cf7bcfe9a48
ARG MTPROTO_REPO_URL=https://github.com/TelegramMessenger/MTProxy

# Environment variables
ENV WORKERS=${WORKERS}
ENV SECRET=${SECRET}
ENV TZ=UTC

# Create working directory
WORKDIR /opt/mtproxy

# Clone and build MTProxy
RUN git clone ${MTPROTO_REPO_URL} . && \
    make && \
    cp objs/bin/mtproto-proxy /usr/local/bin/

# Create directories for configuration and logs
RUN mkdir -p /var/log/mtproxy /var/log/warp /etc/supervisor/conf.d

# Copy configuration files
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY startup.sh /usr/local/bin/startup.sh
COPY healthcheck.sh /usr/local/bin/healthcheck.sh

# Make scripts executable
RUN chmod +x /usr/local/bin/entrypoint.sh /usr/local/bin/startup.sh /usr/local/bin/healthcheck.sh

# Expose MTProxy port
EXPOSE 8889

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Use startup script to initialize and run supervisor
CMD ["/usr/local/bin/startup.sh"]
