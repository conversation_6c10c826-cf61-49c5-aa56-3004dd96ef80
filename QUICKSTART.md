# Quick Start Guide

## 🚀 Get Started in 3 Steps

### 1. <PERSON><PERSON> and Setup
```bash
# If you haven't already, create the files in your directory
# All files should be in the same directory

# Make sure you have Docker and <PERSON>er Compose installed
docker --version
docker-compose --version
```

### 2. Configure (Optional)
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your preferences
nano .env

# Or generate a new secret
make secret
```

### 3. Start the Proxy
```bash
# Start with default settings
docker-compose up -d

# Or use the Makefile
make up

# View logs to get your connection link
make logs
```

## 📱 Connect to Your Proxy

Look for these lines in the logs:
```
MTProxy connection link:
  tg://proxy?server=YOUR_IP&port=443&secret=YOUR_SECRET
  With random padding: tg://proxy?server=YOUR_IP&port=443&secret=ddYOUR_SECRET
```

Copy one of these links and open it in Telegram!

## 🔧 Useful Commands

```bash
# View status
make status

# Check health
make health

# View logs
make logs

# Restart services
make restart

# Stop services
make down

# Clean everything
make clean
```

## ❗ Troubleshooting

### If Telegram shows "Connecting..."
- Check your firewall settings
- Verify the port is open
- Make sure your IP is correct

### If Telegram shows "Updating..."
- Check WARP connection: `docker-compose exec mtproxy-warp warp-cli status`
- Verify system time is synchronized
- Check logs for errors: `make logs`

### Need Help?
- Check the full README.md for detailed information
- View logs: `make logs`
- Check service health: `make health`

## 🎉 Success!

If everything is working, you should see:
- ✅ Container is healthy
- ✅ WARP is connected
- ✅ MTProxy is running
- ✅ Telegram connects successfully

Enjoy your private Telegram proxy with Cloudflare WARP! 🔒
