# MTProxy Configuration
# Copy this file to .env and modify the values as needed

# Secret key for MTProxy (32 hex characters)
# Generate a new one with: head -c 16 /dev/urandom | xxd -ps
SECRET=ec4dd80983dbf12d6b354cf7bcfe9a48

# Number of worker processes (increase for high-traffic servers)
WORKERS=1

# External port to expose MTProxy (default: 443)
PORT=443

# External IP address (optional, auto-detected if not set)
# Set this if auto-detection fails or you're behind NAT
IP=

# Timezone for logs
TZ=UTC

# MTProxy repository URL (optional, use alternative fork if needed)
# Alternative: https://github.com/GetPageSpeed/MTProxy
MTPROTO_REPO_URL=https://github.com/TelegramMessenger/MTProxy
