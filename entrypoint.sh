#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Wait for WARP to be ready
wait_for_warp() {
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if warp-cli --accept-tos status >/dev/null 2>&1; then
            log "WARP service is ready"
            return 0
        fi
        log "Waiting for WARP service... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done

    error "WARP service failed to start after $max_attempts attempts"
    return 1
}

# Function to detect external IP
detect_external_ip() {
    local ip=""

    # Try multiple services to get external IP
    for service in "ifconfig.co" "ipinfo.io/ip" "icanhazip.com" "ident.me"; do
        log "Trying to detect external IP using $service..."
        ip=$(curl -s --connect-timeout 10 --max-time 15 "http://$service" 2>/dev/null | tr -d '\n\r' | grep -E '^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$' || true)
        if [[ -n "$ip" ]]; then
            log "External IP detected: $ip"
            echo "$ip"
            return 0
        fi
    done

    warn "Could not detect external IP automatically"
    return 1
}

# Function to initialize WARP
init_warp() {
    log "Initializing Cloudflare WARP..."

    # Wait for WARP service to be ready
    if ! wait_for_warp; then
        return 1
    fi

    # Check if already registered
    if ! warp-cli --accept-tos status | grep -q "Registration Missing"; then
        log "WARP already registered"
    else
        log "Registering WARP..."
        if ! warp-cli --accept-tos register; then
            error "Failed to register WARP"
            return 1
        fi
        log "WARP registered successfully"
    fi

    # Set WARP to proxy mode
    log "Setting WARP to proxy mode..."
    if ! warp-cli --accept-tos set-mode proxy; then
        error "Failed to set WARP to proxy mode"
        return 1
    fi

    # Connect to WARP
    log "Connecting to WARP..."
    if ! warp-cli --accept-tos connect; then
        error "Failed to connect to WARP"
        return 1
    fi

    # Wait for connection to establish
    sleep 10

    # Verify WARP status
    local status=$(warp-cli --accept-tos status)
    log "WARP Status: $status"

    if echo "$status" | grep -q "Connected"; then
        log "WARP connected successfully"
        return 0
    else
        error "WARP connection failed"
        return 1
    fi
}

# Function to start MTProxy
start_mtproxy() {
    log "Starting MTProxy..."

    # Set default values if not provided
    WORKERS=${WORKERS:-1}
    SECRET=${SECRET:-$(head -c 16 /dev/urandom | xxd -ps)}

    # Detect external IP if not provided
    if [[ -z "$IP" ]]; then
        IP=$(detect_external_ip)
        if [[ -z "$IP" ]]; then
            warn "External IP not detected, MTProxy may not work properly behind NAT"
        fi
    fi

    # Download proxy configuration
    log "Downloading MTProxy configuration..."
    curl -s https://core.telegram.org/getProxySecret -o proxy-secret || {
        error "Failed to download proxy secret"
        exit 1
    }

    curl -s https://core.telegram.org/getProxyConfig -o proxy-multi.conf || {
        error "Failed to download proxy config"
        exit 1
    }

    # Prepare MTProxy arguments
    MTPROXY_ARGS="-u nobody -p 8889 -H 8889 -S $SECRET --aes-pwd proxy-secret proxy-multi.conf"

    # Add NAT info if external IP is available
    if [[ -n "$IP" ]]; then
        MTPROXY_ARGS="$MTPROXY_ARGS --nat-info $IP:8889"
        log "Using external IP: $IP"
    fi

    # Add workers
    MTPROXY_ARGS="$MTPROXY_ARGS -M $WORKERS"

    log "MTProxy configuration:"
    log "  Secret: $SECRET"
    log "  Workers: $WORKERS"
    log "  External IP: ${IP:-'auto-detect'}"
    if [[ -n "$http_proxy" ]]; then
        log "  Using WARP proxy: $http_proxy"
    else
        log "  Direct connection (no proxy)"
    fi

    # Generate connection link
    if [[ -n "$IP" ]]; then
        log "MTProxy connection link:"
        log "  tg://proxy?server=$IP&port=8889&secret=$SECRET"
        log "  With random padding: tg://proxy?server=$IP&port=8889&secret=dd$SECRET"
    fi

    # Start MTProxy
    exec mtproto-proxy $MTPROXY_ARGS
}

# Main execution
main() {
    log "Starting MTProxy with Cloudflare WARP integration..."

    # Initialize WARP
    if ! init_warp; then
        warn "WARP initialization failed, starting MTProxy without WARP"
        # Unset proxy environment variables
        unset http_proxy https_proxy
    else
        log "WARP initialized successfully"
        # Set proxy for MTProxy to use WARP
        export http_proxy="http://127.0.0.1:40000"
        export https_proxy="http://127.0.0.1:40000"
    fi

    # Start MTProxy
    start_mtproxy
}

# Handle signals
trap 'log "Shutting down..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
