.PHONY: help build up down logs restart clean secret status health

# Default target
help:
	@echo "MTProxy with WARP - Available commands:"
	@echo "  build    - Build the Docker image"
	@echo "  up       - Start the services"
	@echo "  down     - Stop the services"
	@echo "  logs     - View logs"
	@echo "  restart  - Restart the services"
	@echo "  clean    - Remove containers and images"
	@echo "  secret   - Generate a new secret"
	@echo "  status   - Show service status"
	@echo "  health   - Check service health"

# Build the Docker image
build:
	docker-compose build --no-cache

# Start services
up:
	docker-compose up -d

# Stop services
down:
	docker-compose down

# View logs
logs:
	docker-compose logs -f mtproxy-warp

# Restart services
restart:
	docker-compose restart

# Clean up
clean:
	docker-compose down -v --rmi all

# Generate a new secret
secret:
	@echo "Generated secret: $$(head -c 16 /dev/urandom | xxd -ps)"

# Show status
status:
	docker-compose ps

# Check health
health:
	@echo "=== Container Health ==="
	docker-compose ps
	@echo ""
	@echo "=== WARP Status ==="
	docker-compose exec mtproxy-warp warp-cli status || echo "WARP not available"
	@echo ""
	@echo "=== MTProxy Logs (last 10 lines) ==="
	docker-compose logs --tail=10 mtproxy-warp
