[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:warp-svc]
command=/usr/bin/warp-svc
autostart=true
autorestart=true
stderr_logfile=/var/log/warp/warp-svc.err.log
stdout_logfile=/var/log/warp/warp-svc.out.log
user=root
priority=100

[program:mtproxy]
command=/usr/local/bin/entrypoint.sh
autostart=true
autorestart=true
stderr_logfile=/var/log/mtproxy/mtproxy.err.log
stdout_logfile=/var/log/mtproxy/mtproxy.out.log
user=root
priority=200
startsecs=30
startretries=3
