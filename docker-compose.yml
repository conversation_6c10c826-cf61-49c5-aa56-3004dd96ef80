version: '3.8'

services:
  mtproxy-warp:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        WORKERS: ${WORKERS:-1}
        SECRET: ${SECRET:-ec4dd80983dbf12d6b354cf7bcfe9a48}
        MTPROTO_REPO_URL: ${MTPROTO_REPO_URL:-https://github.com/TelegramMessenger/MTProxy}
    container_name: mtproxy-warp
    restart: unless-stopped
    ports:
      - "${PORT:-443}:8889"
    environment:
      - WORKERS=${WORKERS:-1}
      - SECRET=${SECRET:-ec4dd80983dbf12d6b354cf7bcfe9a48}
      - TZ=${TZ:-UTC}
      - IP=${IP:-}
    volumes:
      - mtproxy_data:/opt/mtproxy
      - warp_data:/var/lib/cloudflare-warp
    cap_add:
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=0
    dns:
      - *******
      - *******
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8889", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  mtproxy_data:
    driver: local
  warp_data:
    driver: local

networks:
  default:
    driver: bridge
