#!/bin/bash

# Simple health check script for MTProxy
# This script checks if MTProxy is responding on port 8889

# Install net-tools if not available (for netstat)
if ! command -v netstat >/dev/null 2>&1; then
    # Use ss instead of netstat if available
    if command -v ss >/dev/null 2>&1; then
        if ss -tuln | grep -q ":8889 "; then
            echo "MTProxy is listening on port 8889"
            exit 0
        else
            echo "MTProxy is not listening on port 8889"
            exit 1
        fi
    else
        # Fallback to checking if process is running
        if pgrep -f mtproto-proxy >/dev/null; then
            echo "MTProxy process is running"
            exit 0
        else
            echo "MTProxy process is not running"
            exit 1
        fi
    fi
else
    # Check if MTProxy port is listening
    if netstat -tuln | grep -q ":8889 "; then
        echo "MTProxy is listening on port 8889"
        exit 0
    else
        echo "MTProxy is not listening on port 8889"
        exit 1
    fi
fi
